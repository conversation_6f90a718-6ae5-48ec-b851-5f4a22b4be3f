;;; Config from UNA source code
; https://github.com/unacms/una/blob/e3d101dfc2caeae327ba216e6a94cdc773f24f5a/scripts/docker-compose/php.ini
upload_max_filesize = 200M
post_max_size = 200M
html_errors = On
error_prepend_string = "<pre style='white-space: pre-line'>"
error_append_string = "</pre>"

[opcache]
opcache.enable = 1
opcache.revalidate_freq = 0
opcache.validate_timestamps = 1
opcache.max_accelerated_files = 100000
opcache.memory_consumption = 72
opcache.max_wasted_percentage = 20
opcache.interned_strings_buffer = 16
opcache.fast_shutdown = 1

;;; Config to complete missing requirements
; https://unacms.com/wiki/Requirements

; Allow URL file access
allow_url_fopen = On
allow_url_include = Off

; Enable short open tags
short_open_tag = On

; Memory settings
memory_limit = 200M

; Ensure these functions are not disabled
disable_functions =