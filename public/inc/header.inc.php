<?php
header('X-Frame-Options: SAMEORIGIN');

$aPathInfo = pathinfo(__FILE__);
$env = parse_ini_file($aPathInfo['dirname']  . '/../../.env');

if (isset($env['BX_DEBUG']) && $env['BX_DEBUG'])
    ini_set('display_errors', 1);

define('BX_FORCE_AUTOUPDATE_MAX_CHANGED_FILES_PERCENT', 100.0);

define('BX_DOL', 1);

// getenv('BX_DOL_URL_ROOT') or $env['BX_DOL_URL_ROOT'] depending on particular implementation or loading env vars
define('BX_DOL_URL_ROOT', $env['BX_DOL_URL_ROOT']); ///< site ur

define(
    'BX_DIRECTORY_PATH_ROOT',
    rtrim(
        $env['BX_DIRECTORY_PATH_ROOT'] ?? '/data/servers/tc-una-cms-svc/public/',
        '/'
    ) . '/'
); ///< site path

define('BX_DATABASE_HOST', [$env['BX_DATABASE_HOST'], $env['BX_DATABASE_HOST_RO']]); ///< db host
define('BX_DATABASE_SOCK', ['', '']); ///< db socket
define('BX_DATABASE_PORT', ['', '']); ///< db port
define('BX_DATABASE_USER', [$env['BX_DATABASE_USER'], $env['BX_DATABASE_USER_RO']]); ///< db user
define('BX_DATABASE_PASS', [$env['BX_DATABASE_PASS'], $env['BX_DATABASE_PASS_RO']]); ///< db password
define('BX_DATABASE_NAME', [$env['BX_DATABASE_NAME'], $env['BX_DATABASE_NAME_RO']]); ///< db name
define('BX_DATABASE_ENGINE', $env['BX_DATABASE_ENGINE']); ///< db engine
if (isset($env['BX_DATABASE_COLLATE']))
    define('BX_DATABASE_COLLATE', $env['BX_DATABASE_COLLATE']); ///< db collate

define('BX_SYSTEM_JAVA', ''); ///< path to java binary
define('BX_SYSTEM_FFMPEG', '/usr/bin/ffmpeg'); ///< path to ffmpeg binary
define('BX_DOL_SECRET', $env['BX_DOL_SECRET']); ///< secret word

define('BX_DB_FULL_VISUAL_PROCESSING', true); ///< upon db error - show error message
define('BX_DB_FULL_DEBUG_MODE', isset($env['BX_DEBUG']) && $env['BX_DEBUG']); ///< upon db error - show detailed report (turn off in production mode)
define('BX_DB_DO_EMAIL_ERROR_REPORT', false); ///< upon db error - send email with detailed report

error_reporting(E_ALL);
mb_internal_encoding('UTF-8');
mb_regex_encoding('UTF-8');
date_default_timezone_set('UTC');

require_once('params.inc.php');

bx_check_debug_mode();
bx_check_maintenance_mode(true);
bx_check_minimal_requirements(true);
//bx_check_redirect_to_correct_hostname(true);
bx_check_redirect_to_remove_install_folder(true);

