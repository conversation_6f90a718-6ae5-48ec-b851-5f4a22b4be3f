
/* breadcrumb */
div.bx-base-general-breadcrumb  {
    white-space: nowrap;
    overflow: hidden;
}
div.bx-base-general-breadcrumb a,
div.bx-base-general-breadcrumb i {
    display: inline-block;
    vertical-align: top;

    height: 2.2rem;
    line-height: 2.2rem;
}
.bx-base-general-breadcrumb-home i.sys-icon {
    font-size: 2.0rem;
}

/* view entry */
.bx-base-general-entity-info > p:first-child {
    margin-top:0;
}


/* view entry: actions */
.bx-base-general-entity-actions ul.bx-menu-custom-hor {
    position: relative;
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: center;
    align-items: center;
    -webkit-justify-content: flex-start;
    justify-content: flex-start;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
}
.bx-base-general-entity-actions ul.bx-menu-custom-hor.bx-menu-more-auto {
    -webkit-flex-wrap: nowrap;
    flex-wrap: nowrap;
}
.bx-base-general-entity-actions .bx-menu-custom-hor li.bx-menu-item {
    white-space: nowrap;
}
.bx-base-general-entity-actions .bx-menu-custom-hor li.bx-menu-item .cmt-element-holder,
.bx-base-general-entity-actions .bx-menu-custom-hor li.bx-menu-item .cmt-counter-holder,
.bx-base-general-entity-actions .bx-menu-custom-hor li.bx-menu-item .bx-view-element-holder,
.bx-base-general-entity-actions .bx-menu-custom-hor li.bx-menu-item .bx-view-counter-holder,
.bx-base-general-entity-actions .bx-menu-custom-hor li.bx-menu-item .bx-vote-element-holder, 
.bx-base-general-entity-actions .bx-menu-custom-hor li.bx-menu-item .bx-vote-counter-holder, 
.bx-base-general-entity-actions .bx-menu-custom-hor li.bx-menu-item .bx-score-element-holder, 
.bx-base-general-entity-actions .bx-menu-custom-hor li.bx-menu-item .bx-score-counter-holder, 
.bx-base-general-entity-actions .bx-menu-custom-hor li.bx-menu-item .bx-favorite-element-holder,
.bx-base-general-entity-actions .bx-menu-custom-hor li.bx-menu-item .bx-favorite-counter-holder,
.bx-base-general-entity-actions .bx-menu-custom-hor li.bx-menu-item .bx-report-element-holder,
.bx-base-general-entity-actions .bx-menu-custom-hor li.bx-menu-item .bx-report-counter-holder,
.bx-base-general-entity-actions .bx-menu-custom-hor li.bx-menu-item .bx-tl-repost-element-holder,
.bx-base-general-entity-actions .bx-menu-custom-hor li.bx-menu-item .bx-tl-repost-counter-holder {
    float: none;
    display: inline-block;
    vertical-align: top;
}
.bx-base-general-entity-actions .bx-menu-custom-hor li.bx-menu-item .cmt-counter-holder,
.bx-base-general-entity-actions .bx-menu-custom-hor li.bx-menu-item .bx-view-counter-holder,
.bx-base-general-entity-actions .bx-menu-custom-hor li.bx-menu-item .bx-vote-counter-holder, 
.bx-base-general-entity-actions .bx-menu-custom-hor li.bx-menu-item .bx-score-counter-holder, 
.bx-base-general-entity-actions .bx-menu-custom-hor li.bx-menu-item .bx-favorite-counter-holder,
.bx-base-general-entity-actions .bx-menu-custom-hor li.bx-menu-item .bx-report-counter-holder,
.bx-base-general-entity-actions .bx-menu-custom-hor li.bx-menu-item .bx-tl-repost-counter-holder {
    line-height: normal;
}
.bx-base-general-entity-actions .cmt,
.bx-base-general-entity-actions .bx-view,
.bx-base-general-entity-actions .bx-vote,
.bx-base-general-entity-actions .bx-score,
.bx-base-general-entity-actions .bx-favorite,
.bx-base-general-entity-actions .bx-feature,
.bx-base-general-entity-actions .bx-report,
.bx-base-general-entity-actions .bx-tl-repost,
.bx-base-general-entity-actions .bx-menu-btns-hor.bx-menu-object-sys_social_sharing,
.bx-base-general-entity-actions .bx-base-general-entity-action {
    position: relative;
    float: none;
    display: inline-block;
    vertical-align: top;

    margin: 0;
    padding: 0;
}

/* TODO: Remove this at the end */
.bx-base-general-entity-share .bx-base-general-comments,
.bx-base-general-entity-share .bx-view,
.bx-base-general-entity-share .bx-vote,
.bx-base-general-entity-share .bx-score,
.bx-base-general-entity-share .bx-favorite,
.bx-base-general-entity-share .bx-feature,
.bx-base-general-entity-share .bx-report,
.bx-base-general-entity-share .bx-tl-repost,
.bx-base-general-entity-share .bx-menu-btns-hor.bx-menu-object-sys_social_sharing {
	position: relative;
	float: none;
	display: inline-block;
	vertical-align: top;
}

/* attachments */
.bx-base-general-attachments .bx-base-general-attachment video,
.flickity-enabled .flickity-viewport .flickity-slider .bx-base-general-attachment video {
    max-width:100%;
    max-height:100%;
}
.bx-base-general-attachment a div {
    text-overflow: ellipsis;
    overflow: hidden;
}
.bx-base-general-attachment a canvas {
    position:absolute;
    top:0;
    left:0;
}

.bx-base-general-attachment-sound {
    position:relative;
    height:100%;
}
.bx-base-general-attachment-sound-file-name,
.bx-base-general-attachment-sound-player {
  display: flex;
  justify-content: center;
  align-content: center;
  flex-direction: column;
}
.bx-base-general-attachment-sound-file-name {
    height:9.25rem;
}
.bx-base-general-attachment-sound-player {
    height:3.25rem;
}

/* comments item */
div.bx-base-general-comments div.bx-base-general-comments-element-holder,
div.bx-base-general-comments div.bx-base-general-comments-counter-holder {
    position: relative;
    float: left;
}
.bx-base-general-comments-element-holder
.bx-base-general-comments-counter-holder {
    line-height: 20px;
    line-height: 1.25rem;
}
.bx-base-general-comments-button .bx-base-general-comments-element-holder,
.bx-base-general-comments-button .bx-base-general-comments-counter-holder {
    line-height: 36px;
    line-height: 2.2rem;
}
.bx-base-general-comments-button .bx-base-general-comments-counter-holder {
    font-size: 1.5rem;
}
.bx-base-general-comments-link .bx-base-general-comments-counter-holder.bx-btn-border {
    border-width: 0px;
}

/* unit meta */
.bx-base-general-unit-meta {
    white-space: normal;
    overflow: hidden;
    text-overflow: ellipsis;
}
.sys-author ul.bx-menu-custom.bx-menu-custom-hor,
.bx-base-general-unit-meta ul.bx-menu-custom.bx-menu-custom-hor {
    position: relative;
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: flex-start;
    align-items: flex-start;
    -webkit-justify-content: flex-start;
    justify-content: flex-start;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;

    margin: 0.1rem 0 0.2rem 0;
}
.sys-author ul.bx-menu-custom.bx-menu-custom-hor > li,
.bx-base-general-unit-meta ul.bx-menu-custom.bx-menu-custom-hor > li {
    -webkit-flex: 0 1 auto; 
    -ms-flex:  0 1 auto; 
    flex: 0 1 auto;

    white-space: nowrap;
}
.sys-author ul.bx-menu-custom.bx-menu-custom-hor > li,
.bx-base-general-unit-meta ul.bx-menu-custom.bx-menu-custom-hor > li {
    margin-left: 0.5rem;
}
.sys-author ul.bx-menu-custom.bx-menu-custom-hor > li:first-child,
.bx-base-general-unit-meta ul.bx-menu-custom.bx-menu-custom-hor > li:first-child,
.sys-author ul.bx-menu-custom.bx-menu-custom-hor > .bx-menu-item-nl + li,
.bx-base-general-unit-meta ul.bx-menu-custom.bx-menu-custom-hor > .bx-menu-item-nl + li {
    margin-left: 0;
}
.sys-author ul.bx-menu-custom.bx-menu-custom-hor > li.bx-menu-item-nl,
.bx-base-general-unit-meta ul.bx-menu-custom.bx-menu-custom-hor > li.bx-menu-item-nl {
    display: none;
    width: 100%;
    height: 0.5rem;
}
.bx-base-pofile-unit-with-cover .bx-base-general-unit-meta ul.bx-menu-custom.bx-menu-custom-hor > li.bx-menu-item-nl {
    display: block;
}

.sys-author ul.bx-menu-custom.bx-menu-custom-hor > li > span,
.bx-base-general-unit-meta ul.bx-menu-custom.bx-menu-custom-hor > li > span:not(.bx-menu-meta-item-ex) {
    margin-left: 0;
    margin-right: 0;
}

.sys-author ul.bx-menu-custom li:first-child .bx-base-general-unit-meta-div,
.bx-base-general-unit-meta ul.bx-menu-custom li:first-child .bx-base-general-unit-meta-div {
    display: none;
}

.sys-author ul.bx-menu-custom li .bx-base-general-unit-meta-item,
.sys-author ul.bx-menu-custom li .bx-base-general-unit-meta-div,
.sys-author ul.bx-menu-custom li .bx-btn,
.sys-author ul.bx-menu-custom li .bx-vote.bx-vote-stars,
.bx-base-general-unit-meta ul.bx-menu-custom li .bx-base-general-unit-meta-item,
.bx-base-general-unit-meta ul.bx-menu-custom li .bx-base-general-unit-meta-div,
.bx-base-general-unit-meta ul.bx-menu-custom li .bx-btn,
.bx-base-general-unit-meta ul.bx-menu-custom li .bx-vote.bx-vote-stars {
    position: relative;
    float: none;
    display: inline-block;
    vertical-align: middle;
}

.sys-author ul.bx-menu-custom li .bx-vote.bx-vote-stars,
.bx-base-general-unit-meta ul.bx-menu-custom li .bx-vote.bx-vote-stars {
    margin-top: 0px;
    margin-bottom: 0px;
}

.sys-author ul.bx-menu-custom li .bx-metatags-keyword,
.bx-base-general-unit-meta ul.bx-menu-custom li .bx-metatags-keyword {
    margin: 0px;
    line-height: normal;
}

/* Browsing by labels */
.bx-base-general-lables-tree-parent-0, 
.bx-base-general-lables-tree-parent-0 ul {
    list-style-type: none;
    padding-left: 2rem;
}
.bx-base-general-lables-tree-parent-0 {
    margin: 0px;
}
.bx-base-general-lables-tree-parent-0 li {
    padding: 0px;
}
.bx-base-general-lables-tree-parent-0 li i.sys-icon {
    display: inline-block;
    width: 0.625rem;
    margin-left: -1rem;
}


#bx-popup-ajax-wrapper-sys_set_badges li span{
    display: flex;
    margin-left: 0px;
    align-items: baseline;
}
#bx-popup-ajax-wrapper-sys_set_badges li INPUT[type="checkbox"] {
    margin-right: 0.5rem;
}
.bx-base-unit-showcase-wrapper {
    display: block;
    white-space: nowrap;
    overflow: hidden;
}
