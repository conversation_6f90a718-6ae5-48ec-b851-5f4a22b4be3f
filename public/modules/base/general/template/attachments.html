<div class="bx-base-general-attachments bx-clearfix -m-px">
    <bx_repeat:attachments>
        <div class="bx-base-general-attachment bx-def-box bx-def-color-bg-box bx-def-gal-img-size relative m-px text-center" style="float:left;">
            <bx_if:image>
                <a href="javascript:void(0);" title="__attr_file_name__" onclick="$('#__popup_id__').dolPopupImage('__url_original__', $(this).parent())">
                    <img class="bx-def-gal-img-size" src="__url_preview__" />
                </a>
                __popup__
            </bx_if:image>
            <bx_if:video>
                __video__
            </bx_if:video>
            <bx_if:sound>
                __sound__
            </bx_if:sound>
            <bx_if:not_image>
                <a href="__url_original__" title="__attr_file_name__">
                    <div class="bx-def-round-corners bx-def-padding-sec">__file_name__</div>
                </a>
            </bx_if:not_image>
        </div>
    </bx_repeat:attachments>

    <script language="javascript">
        var oAttachments = $('.bx-base-general-attachments');
        if(oAttachments.find('.bx-base-general-attachment').length > 2) {
            oAttachments.flickity({
		cellSelector: '.bx-base-general-attachment',
		cellAlign: 'left',
		pageDots: false,
		imagesLoaded: true
            });
        }
    </script>
    <script type="module">
        // pdf previews
        if ('undefined' !== typeof(window['pdfjsLib'])) {
            $('.bx-base-general-attachment a[href$=".pdf"],.bx-base-general-attachment a[href*=".pdf?"]').each(function () {
                var sUrl = $(this).attr('href');
                var e = $(this);
                var oPdfjsLib = window['pdfjsLib'];
                oPdfjsLib.GlobalWorkerOptions.workerSrc = '__pdfjs_worker_url__';

                var oLoadingTask = pdfjsLib.getDocument(sUrl);
                oLoadingTask.promise.then(function(pdf) {  
                    pdf.getPage(1).then(function(page) {
                        var iDpx = parseInt($.cookie('devicePixelRatio'));
                        if (iDpx != 2)
                            iDpx = 1;
                    
                        e.append('<canvas></canvas>');
                        var eCanvas = e.find('canvas');
                        eCanvas.width(e.parents('.bx-base-general-attachment').innerWidth());
                        eCanvas.height(e.parents('.bx-base-general-attachment').innerHeight());
                        eCanvas[0].width = iDpx * eCanvas.width();
                        eCanvas[0].height = iDpx * eCanvas.height();
                        var oContext = eCanvas[0].getContext('2d');
                        var iWidth = page.getViewport({scale:1.0}).width;
                        var fScaleW = eCanvas[0].width / page.getViewport({scale:1.0}).width;
                        var fScaleH = eCanvas[0].height / page.getViewport({scale:1.0}).height;
                        var fScale = fScaleW < fScaleH ? fScaleW : fScaleH;
                        var oViewport = page.getViewport({scale: fScale, offsetX: (iDpx*eCanvas.width() - iWidth*fScale)/2});

                        var oRenderContext = {
                            canvasContext: oContext,
                            viewport: oViewport,
                        };
                        var oRenderTask = page.render(oRenderContext);
                        oRenderTask.promise.then(function () {
                            e.find('div').remove();
                        });
                    });
                }, function (err) { 
                    console.error(err);
                });
            });
        }
    </script>
</div>
