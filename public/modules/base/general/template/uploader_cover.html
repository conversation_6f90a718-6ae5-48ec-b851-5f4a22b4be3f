<div class="bx-image-edit-buttons __image_exists__ bx-image-edit-buttons-__unique_id__ flex items-center">
    <a href="javascript:" class="bx-btn bx-btn-small bx-image-edit-buttons-edit mx-1" onclick="oBxDolCovers__unique_id__.changePosition()"><bx_image_auto:cog /></a>
    <a href="javascript:" class="bx-btn bx-btn-small bx-image-edit-buttons-save mx-1 hidden" onclick="oBxDolCovers__unique_id__.savePosition()"><bx_image_auto:save /></a>
    <a href="javascript:" class="bx-btn bx-btn-small bx-image-edit-buttons-cancel mx-1 hidden" onclick="oBxDolCovers__unique_id__.cancelPosition()"><bx_image_auto:arrows-alt /></a>
    <a href="javascript:" class="bx-btn bx-btn-small bx-image-edit-buttons-upload mx-1" onclick="oBxDolCovers__unique_id__.showUploaderForm()"><bx_image_auto:upload /></a>
</div>
<div class="hidden">
    <div id="bx-form-input-files-__unique_id__-upload-result"></div>
    __uploader__
    __uploader_js__
</div>
<script>
$("#bx-form-input-files-__unique_id__-upload-result").bind( "contentchange", function(){ 
    oBxDolCovers__unique_id__.uploadComplete($(this));
});
    
function BxDolCovers (unique_id, action_url, content_id, field, js_object) {
    this._sUniqueId = unique_id;
    this._sActionUrl = action_url;
    this._sContentId = content_id;
    this._sField = field;
    this._sJsObject = js_object;
    this._oContainerButtons = $('.bx-image-edit-buttons-' + unique_id);
}

BxDolCovers.prototype.showUploaderForm = function(){
    var $this = this;
    $('#bx-form-input-files-' + $this._sUniqueId + '-upload-result').html('');
    eval($this._sJsObject + '.showUploaderForm()');
}
    
BxDolCovers.prototype.uploadComplete = function(obj){
    var $this = this;
    var sUrl = $this._sActionUrl + "updateImage/" + $this._sField + "/" + $this._sContentId + "/" + obj.html() + "/"; 
    
    $this._oContainerButtons.removeClass('bx-image-edit-buttons-no-image');
    
    $.post(sUrl, function (aData) {
        $(".bx-image-edit-source-" + $this._sUniqueId).css("background-image", "url(" + aData + ")").css('background-position', '');
    });
}

BxDolCovers.prototype.changePosition = function (){
    var $this = this;
    $(".bx-image-edit-source-" + $this._sUniqueId).addClass('bx-image-edit-move').bind('dragover', function(e){
        $(".bx-image-edit-source-" + $this._sUniqueId).css('background-position', " 0px " + e.offsetY/500*100 + '%');
    });
    $this._oContainerButtons.find('.bx-image-edit-buttons-cancel').removeClass('hidden');
    $this._oContainerButtons.find('.bx-image-edit-buttons-save').removeClass('hidden');
    $this._oContainerButtons.find('.bx-image-edit-buttons-edit').addClass('hidden');
    $this._oContainerButtons.find('.bx-image-edit-buttons-upload').addClass('hidden');
}

BxDolCovers.prototype.cancelPosition = function (){
    var $this = this;
    $this._oContainerButtons.find('.bx-image-edit-buttons-cancel').addClass('hidden');
    $this._oContainerButtons.find('.bx-image-edit-buttons-save').addClass('hidden');
    $this._oContainerButtons.find('.bx-image-edit-buttons-edit').removeClass('hidden');
    $this._oContainerButtons.find('.bx-image-edit-buttons-upload').removeClass('hidden');
}
    
BxDolCovers.prototype.savePosition = function (){
    var $this = this;
    
    $(".bx-image-edit-source-" + $this._sUniqueId).removeClass('bx-image-edit-move')
    
    aPos = $(".bx-image-edit-source-" + $this._sUniqueId).css('background-position').split(' ');
    $.post($this._sActionUrl + 'updateImagePosition/' + $this._sContentId + '/' + $this._sField + '/' + aPos[0].replace('%','').replace('px','') + '/' + aPos[1].replace('%','').replace('px','') + '/', function () {
        $this._oContainerButtons.find('.bx-image-edit-buttons-cancel').addClass('hidden');
        $this._oContainerButtons.find('.bx-image-edit-buttons-save').addClass('hidden');
        $this._oContainerButtons.find('.bx-image-edit-buttons-edit').removeClass('hidden');
        $this._oContainerButtons.find('.bx-image-edit-buttons-upload').removeClass('hidden');
    });
}

oBxDolCovers__unique_id__ = new BxDolCovers('__unique_id__', '__action_url__', '__id__', '__field__', '__js_object__');
    
</script>