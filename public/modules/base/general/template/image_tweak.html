<div class="bx-image-edit-buttons __image_exists__ bx-image-edit-buttons-__unique_id__ flex items-center hidden">
    <a href="javascript:" class="bx-btn bx-btn-small bx-image-edit-buttons-edit mx-1 hidden" title="<bx_text:_sys_uploader_image_reposition />" onclick="oBxDolImageTweak__unique_id__.changePosition()"><bx_image_auto:arrows-alt /></a>
    <a href="javascript:" class="bx-btn bx-btn-small bx-image-edit-buttons-save mx-1 hidden" title="<bx_text:_sys_uploader_image_reposition_save />" onclick="oBxDolImageTweak__unique_id__.savePosition()"><bx_image_auto:check /></a>
    <a href="javascript:" class="bx-btn bx-btn-small bx-image-edit-buttons-cancel mx-1 hidden" title="<bx_text:_sys_uploader_image_reposition_cancel />" onclick="oBxDolImageTweak__unique_id__.cancelPosition()"><bx_image_auto:times /></a>
    <a href="javascript:" class="bx-btn bx-btn-small bx-image-edit-buttons-upload mx-1" title="<bx_text:_sys_uploader_image_upload />" onclick="oBxDolImageTweak__unique_id__.showUploaderForm()"><bx_image_auto:upload /></a>
    __uploader__
</div>
<div class="hidden">
    <div id="bx-form-input-files-__unique_id__-upload-result"></div>
</div>
__uploader_js__
<script> 
    oBxDolImageTweak__unique_id__ = new BxDolImageTweak('__unique_id__', '__action_url__', '__id__', '__field__', '__js_object__', __allow_tweak__);
</script>