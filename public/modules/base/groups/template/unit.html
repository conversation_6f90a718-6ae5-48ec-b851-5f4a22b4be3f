<div class="__class__ p-2 box-border">
    <div class="bx-base-pofile-unit-cnt bx-def-box bx-def-box-round-corners bx-def-color-bg-box relative h-full overflow-hidden">
        <div class="absolute z-0 w-full h-40 bg-gradient-to-b from-green-400 to-green-500"></div>
        <a class="bx-base-pofile-unit-cover relative flex items-center justify-start w-full h-40 bg-no-repeat bg-center bg-cover hover:no-underline" style="background-image:url(__cover_url__); __cover_settings__" href="__content_url__" onclick="__content_click__">
            <bx_if:show_thumbnail>
                <div class="bx-def-unit-img bx-def-__size__-size __class_size__ relative ml-5">
                    <bx_if:show_thumb_image>
                        <img class="bx-base-pofile-unit-thumb bx-def-__size__ bx-def-__size__-size __class_size__ ring-4 ring-white dark:ring-gray-900 rounded-md box-border" src="__thumb_url__" />
                    </bx_if:show_thumb_image>
                    <bx_if:show_thumb_letter>
                        <p class="bx-base-pofile-unit-thumb bx-def-__size__ bx-def-__size__-size __class_size__ flex items-center justify-center m-0 text-white font-bold ring-4 ring-white dark:ring-gray-900 rounded-md box-border" style="background-color:rgba(__color__)">__letter__</p>
                    </bx_if:show_thumb_letter>
                    <bx_if:show_online>
                        <div class="bx-base-pofile-unit-online"></div>
                    </bx_if:show_online>
                </div>
            </bx_if:show_thumbnail>
        </a>
        <div class="bx-base-pofile-unit-info p-4 text-left">
            <div class="bx-base-pofile-unit-title">
                <div class="bx-base-put-title truncate">
                    <a href="__content_url__" onclick="__content_click__" title="__title_attr__" class="hover:underline text-gray-700 dark:text-gray-200 hover:text-gray-900 dark:hover:text-white text-xl font-semibold">__title__</a>__badges__
                </div>
                <div class="bx-base-put-addon text-sm text-gray-500 dark:text-gray-400">__addon__</div>
            </div>
            <bx_if:meta>
                <div class="bx-base-general-unit-meta flex flex-col items-stretch justify-center -mx-1 mt-2">__meta__</div>
            </bx_if:meta>
        </div>
    </div>
</div>