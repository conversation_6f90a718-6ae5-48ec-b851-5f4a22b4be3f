<div class="bx-base-profile-cover-block-wrapper">
    <div class="bx-base-pofile-cover-wrapper __class__ bx-def-centered bx-def-box-sizing">
        <div class="bx-base-pofile-cover bx-def-cover-color-bg bx-def-box-sizing <bx_if:show_clickable>bx-lnk-cnt</bx_if:show_clickable>">
            <a href="__content_url__" class="bx-lnk-src hidden"></a>
            <bx_if:show_cover>
                <div class="bx-base-pofile-cover-image __add_class__">
                    <p class="bx-base-pofile-cover-image bx-def-box-sizing flex justify-center items-center">
                        <img class="bx-base-pofile-cover-image h-full object-cover bg-cover w-full __img_class__ bx-image-edit-source-__unique_id__" style="background-image:url(__cover_url__);__cover_settins__" src="<bx_icon_url:spacer.gif />"/>
                    </p>
                    
                </div>
            </bx_if:show_cover>
            <div class="bx-base-pofile-cover-info">
                <div class="bx-base-pofile-ci-cnt bx-def-padding-leftright">
                    <bx_if:show_avatar>
                        <div class="bx-base-pofile-cover-thumb __add_class__">
                            <a class="bx-base-pofile-cover-thumb" href="javascript:" <bx_if:is_avatar>onclick="$('#__picture_popup_id__').dolPopupImage('__picture_url__', $(this).parent()); event.stopPropagation();"</bx_if:is_avatar>>
                                <img class="bx-base-pofile-unit-thumb bx-def-ava-big bx-def-ava-big-size rounded-full ring-4 mx-auto ring-white dark:ring-gray-900 __img_class__ bx-image-edit-source-__unique_id__" src="__ava_url__" <bx_if:show_ava_letter> style="display:none" </bx_if:show_ava_letter> />
                                <p class="bx-base-pofile-unit-thumb bx-def-ava-big bx-def-ava-big-size flex items-center justify-center box-border m-0 rounded-full ring-4 mx-auto ring-white dark:ring-gray-900 text-white font-bold text-5xl __img_class__-placeholder" style="background-color:rgba(__color__);<bx_if:show_ava_image>display:none</bx_if:show_ava_image>" >__letter__</p>
                            </a>
                            <bx_if:show_online>
                                <div class="bx-base-pofile-unit-online"></div>
                            </bx_if:show_online>
                            __picture_tweak__
                        </div>
                    </bx_if:show_avatar>
                </div>
                <div class="bx-base-pofile-cover-user bx-def-margin-sec-topbottom">
                    <div class="bx-base-profile-cover-title-bages">
                        <bx_if:show_title_as_tag>
                            <h1 class="bx-def-unit-info">__title__</h1>
                        </bx_if:show_title_as_tag>
                        <bx_if:show_title_as_text>
                            <div class="bx-def-unit-info bx-def-font-h1">__title__</div>
                        </bx_if:show_title_as_text>
                        <div class="bx-base-profile-cover-bages bx-base-bages-container">__badges__</div>
                    </div>
                    <div class="bx-base-pofile-cover-actions bx-def-padding-sec-leftright bx-def-box-sizing" onclick="event.stopPropagation();">__action_menu__</div>
                    <bx_if:show_text>
                        <div class="bx-def-padding-leftright bx-def-padding-sec-bottom bx-def-unit-desc">__text__</div>
                    </bx_if:show_text>
                </div>
                __show_data__
            </div>
            __cover_tweak__
        </div>
    </div>
    __picture_popup__
</div>
__additional_code__