<div id="__form_container_id__" class="bx-uploader-form-cont bx-popup-width">
	__file_field__

    <div class="bx-uploader-loading"></div>
    <div class="bx-def-padding-sec-bottom bx-croppie-msg"></div>
    <div class="bx-def-padding-sec-bottom" id="__errors_container_id__"></div>

    <div class="bx-croppie-element bx-base-general-croppie-element bx-def-margin-topbottom"></div>

    <div class="bx-def-padding-top bx-clearfix">
        <button class="bx-crop-action bx-btn bx-btn-disabled bx-def-margin-sec-right bx-def-margin-sec-top bx-crop-upload bx-btn-primary"><bx_text:_sys_uploader_crop_submit_button /></button>
        <button class="bx-crop-action bx-btn bx-btn-disabled bx-def-margin-sec-right bx-def-margin-sec-top bx-crop-rotate" data-deg="-90"><bx_text:_sys_uploader_crop_rotate_left /></button>
        <button class="bx-crop-action bx-btn bx-btn-disabled bx-def-margin-sec-right bx-def-margin-sec-top bx-crop-rotate" data-deg="90"><bx_text:_sys_uploader_crop_rotate_right /></button>
        <div class="bx-btn bx-def-margin-sec-top" onclick="__uploader_instance_name__.onClickCancel()"><bx_text:_sys_uploader_close /></div>
    </div>

</div>

<script>
setTimeout(function () {    
    __uploader_instance_name__.initUploader({
        bx_form: {
            uo: "__engine__",
            so: "__storage_object__",
            uid: "__uniq_id__",
            m: "__multiple__",
            c: "__content_id__",
            p: "__storage_private__",
            a: "upload"
        },
        viewport: {
            width: 240,
            height: 120,
            // type: 'circle'
        },
        boundary: {
            width: 280,
            height: 160
        },
        enableOrientation: true
    });
}, 100);
</script>
