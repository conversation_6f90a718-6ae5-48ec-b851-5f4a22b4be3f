<div class="bx-content-description-before">__content_description_before__</div>
<div class="bx-content-description">
    <bx_if:show_image>
        <div class="bx-base-text-entity-image mb-4 text-center __add_class__ relative flex justify-center items-center">
            <img class="rounded __img_class__ w-full aspect-video object-cover bg-cover bx-image-edit-source-__unique_id__" style="background-image:url(__entry_image__); __image_settings__"  src="<bx_icon_url:spacer.gif />"/>
            __image_tweak__
        </div>
        
        __additional_code__
    </bx_if:show_image>
    <div class="bx-base-text-entity-content bx-def-constraint-list bx-def-constraint-pre bx-def-constraint-object bx-def-constraint-iframe bx-def-constraint-img bx-def-vanilla-html">
        <div class="bx-base-text-title-bages">
            <h1 class="text-ellipsis overflow-hidden">__entry_title__</h1>
            <div class="bx-base-bages-container absolute flex -translate-y-5/4">__badges__</div>
        </div>
        <bx_if:show_entry_abstract>
            <div class="bx-base-text-abstract mb-3 md:mb-4 lg:mb-6 text-gray-600 dark:text-gray-400 text-xl font-medium">__entry_abstract__</div>
        </bx_if:show_entry_abstract>
        <div class="bx-base-text-text text-ellipsis overflow-hidden">__entry_text__</div>
    </div>
    <bx_if:show_links>
        <div class="__style_prefix__-item-links">
            <bx_repeat:links>
                <div class="__style_prefix__-item-link mt-3 md:mt-4 lg:mt-6">__link__</div>
            </bx_repeat:links>
        </div>
    </bx_if:show_links>
</div>
<div class="bx-content-description-after">__content_description_after__</div>