<div id="bx-uploader-file-{storage_object}-{file_id}" class="bx-uploader-ghost flex items-start mt-2">
    <div class="bx-uploader-ghost-cnt">
        <div class="bx-base-general-icon-wrapper">
            <img src="{file_icon}" class="bx-base-general-icon bx-base-general-icon-{file_id} rounded" />
            <input type="hidden" name="__name__[]" value="{file_id}" />
        </div>
        <span class="bx-base-general-uploader-ghost">
            <span id="bx-base-general-intert-to-post-link-{file_id}" style="display:none;">
                <a class="bx-btn bx-btn-small mr-2" href="javascript:void(0);" title = "<bx_text:_sys_txt_form_entry_input_picture_insert />" onclick="bx_base_general_insert_to_post('{file_id}', '{file_url}', '__editor_id__', '{file_type}', '__embed_url__');">
                    <bx_image_auto:plus />
                </a>
            </span>
            <a class="bx-btn bx-btn-small mr-2" href="javascript:void(0);" title="<bx_text:_sys_txt_form_entry_input_picture_delete /" onclick="bx_base_general_delete_ghost('{file_id}', '{file_url}', '{file_icon}', ['__editor_id__'], {js_instance_name});">
                <bx_image_auto:times />
            </a>
            <a class="bx-btn bx-btn-small mr-2" href="javascript:void(0);" title="<bx_text:_sys_txt_form_entry_input_picture_copy />" onclick="bx_copy_to_clipboard('{file_url}');">
                <bx_image_auto:copy />
            </a>
        </span>
    </div>
</div>
<script>
    $('#bx-base-general-intert-to-post-link-{file_id}').show();

    // disable images drag&drop, because resized images address can be reset after some time
    $(".bx-base-general-icon-{file_id}").bind('dragstart', function(){
        return false; 
    });
</script>
