<div id="bx-uploader-file-{storage_object}-{file_id}" class="bx-uploader-ghost flex items-start mt-2">
    <div class="bx-uploader-ghost-cnt">
        <input type="hidden" name="__name__[]" value="{file_id}" />
        <div class="bx-base-general-icon-wrapper">
            <img src="{file_icon}" class="bx-base-general-icon rounded bx-base-general-icon-{file_id}" />
        </div>
        <span class="bx-base-general-uploader-ghost">
            <span id="bx-base-general-intert-to-post-link-{file_id}" class="inline-block" style="display:none;">
                <a class="bx-btn bx-btn-small mr-2" href="javascript:void(0);" title="<bx_text:_sys_txt_form_entry_input_picture_insert />" onclick="bx_base_general_insert_to_post('{file_id}', '{file_url}', '__editor_id__');">
                    <bx_image_auto:plus />
                </a>
            </span>
            <a class="bx-btn bx-btn-small mr-2" href="javascript:void(0);" title="<bx_text:_sys_txt_form_entry_input_picture_delete />" onclick="bx_base_general_delete_ghost('{file_id}', '{file_url}', '{file_icon}', ['__editor_id__'], {js_instance_name});">
                <bx_image_auto:times />
            </a>
            <a class="bx-btn bx-btn-small mr-2" href="javascript:void(0);" title="<bx_text:_sys_txt_form_entry_input_picture_copy />" onclick="bx_copy_to_clipboard('{file_url}');">
                <bx_image_auto:copy />
            </a>
        </span>
    </div>
    <bx_if:set_thumb>
        <span class="bx-base-general-use-as-thumb inline-block">
            <input type="checkbox" name="__name_thumb__[]" value="{file_id}" onclick="bx_base_general_select_thumb(this)" id="__name_thumb__-{file_id}" /> 
            <label for="__name_thumb__-{file_id}">__txt_pict_use_as_thumb__</label>
        </span>
    </bx_if:set_thumb>
</div>
<script>
    $('#bx-base-general-intert-to-post-link-{file_id}').show();

    $('#bx-uploader-file-{storage_object}-{file_id} .bx-base-general-use-as-thumb input[value="__thumb_id__"]').attr('checked', true);

    // disable images drag&drop, because resized images address can be reset after some time
    $(".bx-base-general-icon-{file_id}").bind('dragstart', function(){
        return false; 
    });

    // select first image as a thumb by default
    if (!__content_id__ && 1 == $('input[name="__name_thumb__[]"]').length)
        $('input[name="__name_thumb__[]"]').attr('checked', true);
</script>