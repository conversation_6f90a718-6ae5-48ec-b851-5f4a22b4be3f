<div id="bx-uploader-file-{storage_object}-{file_id}" class="bx-uploader-ghost flex items-start mt-2">
    <div class="bx-uploader-ghost-cnt">
        <input type="hidden" name="__name__[]" value="{file_id}" />
        <input type="hidden" name="__name_thumb__[]" value="{file_id}" onclick="bx_base_general_select_thumb(this)" id="__name_thumb__-{file_id}" />
        <div class="bx-base-general-icon-wrapper">
            <img src="{file_icon}" class="bx-base-general-icon bx-base-general-icon-{file_id} rounded" />
        </div>
        <span class="bx-base-general-uploader-ghost">
            <a class="bx-btn bx-btn-small mr-2" href="javascript:void(0);" title="<bx_text:_sys_txt_form_entry_input_picture_delete />" onclick="bx_base_general_delete_ghost('{file_id}', '{file_url}', '{file_icon}', ['__editor_id__'], {js_instance_name});">
                <bx_image_auto:times />
            </a>
        </span>
    </div>
</div>
<script>
    // disable images drag&drop, because resized images address can be reset after some time
    $(".bx-base-general-icon-{file_id}").bind('dragstart', function(){
        return false; 
    });
</script>
