/*-- Poll form: Answers custom element --*/
.bx-form-input-answer {
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: center;
    align-items: center;
}
.bx-form-input-answer.bx-fi-answer-blank {
    display: none;
}
.bx-form-input-answer-cell.bx-fi-sc-txt {
    -webkit-flex: 1 1 auto;
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
}
.bx-form-input-answer-cell.bx-fi-sc-btn {
    -webkit-flex: 0 0 auto;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
}
.bx-form-input-answer-add {
    text-align: right;
}
.bx-form-input-answer-add .bx-form-input-button {
    display: inline-block;
    float: none;
}


/*--- Polls list ---*/
.bx-base-text-polls {
    position: relative;

    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
}

.bx-base-text-polls .bx-base-text-poll {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;

    -webkit-box-flex: 0;

    -webkit-flex: 0 0 100%;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;

    overflow:hidden;
}

.bx-base-text-polls-showcase {
    position: relative;
}

.bx-base-text-polls-showcase .flickity-slider {
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: center;
    align-items: center;
}

.bx-base-text-polls-showcase .bx-base-text-poll {
    position: relative;
    width: 100%;
}

/* Media: phone2 */
.bx-media-phone2 .bx-layout-column-half .bx-base-text-polls.bx-base-text-autosizing .bx-base-text-poll,
.bx-media-phone2 .bx-layout-column-thin .bx-base-text-polls.bx-base-text-autosizing .bx-base-text-poll {
    -webkit-flex: 0 0 49.8%;
    -ms-flex: 0 0 49.8%;
    flex: 0 0 49.8%;
}

/* Media: tablet2 */
.bx-media-tablet2 .bx-layout-column-wide .bx-base-text-polls.bx-base-text-autosizing .bx-base-text-poll,
.bx-media-tablet2 .bx-layout-column-fullwidth .bx-base-text-polls.bx-base-text-autosizing .bx-base-text-poll {
    -webkit-flex: 0 0 49.8%;
    -ms-flex: 0 0 49.8%;
    flex: 0 0 49.8%;
}

/* Media: desktop */
.bx-media-desktop .bx-layout-column-wide .bx-base-text-polls.bx-base-text-autosizing .bx-base-text-poll {
    -webkit-flex: 0 0 49.8%;
    -ms-flex: 0 0 49.8%;
    flex: 0 0 49.8%;
}
.bx-media-desktop .bx-layout-column-fullwidth .bx-base-text-polls.bx-base-text-autosizing .bx-base-text-poll {
    -webkit-flex: 0 0 33.25%;
    -ms-flex: 0 0 33.25%;
    flex: 0 0 33.25%;
}


/*--- Poll item ---*/
.bx-base-text-poll {
    position: relative;
}
.bx-base-text-poll-cnt {
    position: relative;
    width: 100%;
}
.bx-base-text-poll-question p {
    margin: 0;
    padding: 0;
}

/*+++ Poll item: Actions +++*/
.bx-base-text-poll-actions {
    position: relative;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-justify-content: flex-end;
    justify-content: flex-end;
    -webkit-align-items: center;
    align-items: center;

    top: 0;
    right: 0;
}
.bx-base-text-poll-action {
    position: relative;
    display: block;
}
.bx-base-text-poll-actions li.bx-def-color-bg-hl-hover {
    background-color: transparent;
}
.bx-base-text-poll-actions li.bx-menu-inter-act,
.bx-base-text-poll-actions li.bx-menu-inter-pas {
    margin-left: 0.5rem;
}
.bx-base-text-poll-actions li.bx-menu-inter-act div, 
.bx-base-text-poll-actions li.bx-menu-inter-pas div{
    padding: 0;
}
.bx-base-text-poll-actions li.bx-menu-inter-div {
    display: none;
}

/*+++ Poll item: Results +++*/
div.bx-base-text-pr-graph {
	display: table;
	width: 100%;
}
div.bx-base-text-pr-graph .bx-base-text-prg-bar,
div.bx-base-text-pr-graph .bx-base-text-prg-text {
	display: table-cell;
}
div.bx-base-text-pr-graph .bx-base-text-prg-bar {
    -webkit-animation-fill-mode: both;
    -moz-animation-fill-mode: both;
    -o-animation-fill-mode: both;
    animation-fill-mode: both;

    -webkit-transform-origin: left;
    -moz-transform-origin: left;
    -ms-transform-origin: left;
    -o-transform-origin: left;
    transform-origin: left;

    -webkit-animation: bx-base-text-prg-bar-anim 1000ms ease-out;
    -o-animation: bx-base-text-prg-bar-anim 1000ms ease-out;
    animation: bx-base-text-prg-bar-anim 1000ms ease-out;

    background: #2196F3;
    background: -moz-linear-gradient(left,#2196F3 0%,#1976D2 100%);
    background: -webkit-gradient(linear,left top,right top,color-stop(0%,#2196F3),color-stop(100%,#1976D2));
    background: -webkit-linear-gradient(left,#2196F3 0%,#1976D2 100%);
    background: -o-linear-gradient(left,#2196F3 0%,#1976D2 100%);
    background: -ms-linear-gradient(left,#2196F3 0%,#1976D2 100%);
    background: linear-gradient(to right,#2196F3 0%,#1976D2 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#2196F3',endColorstr='#1976D2',GradientType=1);
}
div.bx-base-text-pr-graph .bx-base-text-prg-text {
	white-space: nowrap;
}

/*--- Poll item embed ---*/
.bx-base-text-poll-embed {
    width: 100%;
    height: 240px;
}

.bx-page-iframe .bx-base-text-poll {
    width: 80vw;
    height: 100vh;
}
.bx-page-iframe .bx-base-text-showcase-type-single .bx-base-text-poll {
    width: 100%;
}
.bx-page-iframe .bx-base-text-poll-cnt {
    height: 100%;
    box-sizing: border-box;
}
.bx-page-iframe .bx-base-text-poll-plate {
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
}


/*--- Animation ---*/
@-webkit-keyframes bx-base-text-prg-bar-anim {
    from {
        -webkit-transform:scaleX(0);
        -moz-transform:scaleX(0);
        -ms-transform:scaleX(0);
        -o-transform:scaleX(0);
        transform:scaleX(0)
    }
    to {
        -webkit-transform:scaleX(1);
        -moz-transform:scaleX(1);
        -ms-transform:scaleX(1);
        -o-transform:scaleX(1);
        transform:scaleX(1)
    }
}
@-moz-keyframes bx-base-text-prg-bar-anim {
    from {
        -webkit-transform:scaleX(0);
        -moz-transform:scaleX(0);
        -ms-transform:scaleX(0);
        -o-transform:scaleX(0);
        transform:scaleX(0)
    }
    to {
        -webkit-transform:scaleX(1);
        -moz-transform:scaleX(1);
        -ms-transform:scaleX(1);
        -o-transform:scaleX(1);
        transform:scaleX(1)
    }
}
@-ms-keyframes bx-base-text-prg-bar-anim {
    from {
        -webkit-transform:scaleX(0);
        -moz-transform:scaleX(0);
        -ms-transform:scaleX(0);
        -o-transform:scaleX(0);
        transform:scaleX(0)
    }
    to {
        -webkit-transform:scaleX(1);
        -moz-transform:scaleX(1);
        -ms-transform:scaleX(1);
        -o-transform:scaleX(1);
        transform:scaleX(1)
    }
}
@keyframes bx-base-text-prg-bar-anim {
    from {
        -webkit-transform:scaleX(0);
        -moz-transform:scaleX(0);
        -ms-transform:scaleX(0);
        -o-transform:scaleX(0);
        transform:scaleX(0)
    }
    to {
        -webkit-transform:scaleX(1);
        -moz-transform:scaleX(1);
        -ms-transform:scaleX(1);
        -o-transform:scaleX(1);
        transform:scaleX(1)
    }
}