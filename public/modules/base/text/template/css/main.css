/* unit */
.bx-base-text-unit h2,
.bx-base-text-unit h1 {
    margin:0;
}

.bx-base-text-unit .sys-icon {
    vertical-align:middle;
}

.bx-base-text-unit-cnt {
    width: 100%;
}

.bx-base-text-unit-no-thumb,
.bx-base-text-unit-thumb {
    display:block;
    overflow:hidden;    
}

.bx-base-text-unit-title,
.bx-base-text-unit-summary {
    overflow: hidden;
    text-overflow: ellipsis;
}

.bx-base-text-unit-summary p {
    margin:0;
}

.bx-base-text-unit-cover {
    position: relative;
}
.bx-base-text-unit-text {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}
.bx-base-text-unit-title {
    display: flex;
    white-space: nowrap;
}
.bx-media-phone .bx-base-text-unit-title {
    white-space: normal;
}

.bx-base-text-unit-title,
.bx-base-text-unit-title h2 {
    text-overflow: ellipsis;
    overflow: hidden;
}

.bx-base-text-unit-cover img {
    position: relative;
    display: block;

    width: 100%;
    min-height: 300px;
    object-fit: cover;
}
.bx-media-phone .bx-base-text-unit-cover img {
    height: 150px;
}

.bx-base-text-unit-info {
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
}

.bx-base-text-unit-date {
    white-space: nowrap;
}

.bx-base-text-unit-gallery .bx-base-text-unit-no-thumb,
.bx-base-text-unit-gallery .bx-base-text-unit-thumb {
    float:none;
    display:block;
    width:100%;

    background-size:cover;
    background-repeat:no-repeat;
    background-position:center center;

    line-height:1.25em;
}

.bx-base-text-unit-gallery h2 {
    margin-bottom: 0;
    max-height: 3.75em;
    line-height: 1.25em;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-align: left;
    overflow: hidden;
}

.bx-base-text-wrapper {
    margin-bottom:0;
}

.bx-base-text-unit-ext .bx-base-text-unit-thumb {
    float:left;
}

.bx-base-text-unit-strecher {
    visibility: hidden;
}

/* unit meta */
.bx-base-general-unit-meta ul.bx-menu-custom.bx-menu-custom-hor > li {
    margin-left: 0;
}


/* flexbox */
.bx-base-text-unit-gallery-wrapper {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;

    /* makes all items on a row equal height */
    -webkit-box-align: stretch;
    -webkit-align-items: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;

    /* wrap onto the next line rather than stretching or shrinking */
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;

    /* tells flexbox to start items from the left */
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    flex-pack: start;
    -webkit-justify-content: flex-start;
    justify-content: flex-start;
}

.bx-base-text-unit-gallery-wrapper .bx-base-text-unit-gallery {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;

    -webkit-box-flex: 0;

    -webkit-flex: 0 0 100%;
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;

    overflow:hidden;
}

/* MEDIA: phone2 */

.bx-media-phone2 .bx-layout-column-half .bx-base-text-unit-gallery-wrapper .bx-base-text-unit-gallery,
.bx-media-phone2 .bx-layout-column-thin .bx-base-text-unit-gallery-wrapper .bx-base-text-unit-gallery {
    -webkit-flex: 0 0 49.8%;
    -ms-flex: 0 0 49.8%;
    flex: 0 0 49.8%;
}

.bx-media-phone2 .bx-base-unit-showcase-wrapper .bx-base-text-unit-showcase {
   width:calc(50%);
}

/* MEDIA: tablet2 */

.bx-media-tablet2 .bx-layout-column-wide .bx-base-text-unit-gallery-wrapper .bx-base-text-unit-gallery,
.bx-media-tablet2 #sys_search_results .bx-base-text-unit-gallery-wrapper .bx-base-text-unit-gallery,
.bx-media-tablet2 .bx-layout-column-fullwidth .bx-base-text-unit-gallery-wrapper .bx-base-text-unit-gallery {
    -webkit-flex: 0 0 49.8%;
    -ms-flex: 0 0 49.8%;
    flex: 0 0 49.8%;
}

.bx-media-tablet2 .bx-base-unit-showcase-wrapper .bx-base-text-unit-showcase {
   width:calc(50%);
}

/* MEDIA: desktop */

.bx-media-desktop .bx-layout-column-wide .bx-base-text-unit-gallery-wrapper .bx-base-text-unit-gallery {
    -webkit-flex: 0 0 49.8%;
    -ms-flex: 0 0 49.8%;
    flex: 0 0 49.8%;
}
.bx-media-desktop #sys_search_results .bx-base-text-unit-gallery-wrapper .bx-base-text-unit-gallery,
.bx-media-desktop .bx-layout-column-fullwidth .bx-base-text-unit-gallery-wrapper .bx-base-text-unit-gallery {
    -webkit-flex: 0 0 33.25%;
    -ms-flex: 0 0 33.25%;
    flex: 0 0 33.25%;
}

.bx-media-desktop .bx-layout-column-thin .bx-base-unit-showcase-wrapper .bx-base-text-unit-showcase {
   width:calc(100%);
}

.bx-media-desktop .bx-layout-column-half .bx-base-unit-showcase-wrapper .bx-base-text-unit-showcase,
.bx-media-desktop .bx-layout-column-wide .bx-base-unit-showcase-wrapper .bx-base-text-unit-showcase {
   width:calc(50%);
}

.bx-media-desktop #sys_search_results  .bx-base-unit-showcase-wrapper .bx-base-text-unit-showcase,
.bx-media-desktop .bx-layout-column-fullwidth  .bx-base-unit-showcase-wrapper .bx-base-text-unit-showcase {
   width:calc(33.3%);
}


    
/* view page */
.bx-base-text-title-bages h1 {
    overflow: hidden;
    text-overflow: ellipsis;
}

.bx-content-description-after .bx-vote-counter-wrapper {
    padding-top: 1rem;
    line-height: 2.5rem;
    font-size: 1.5rem;
}

.bx-base-text-entity-image img {
    width: 100%;
}

.bx-base-text-entity-actions-all .bx-base-text-entity-share,
.bx-base-text-entity-actions-all .bx-menu-btns-hor {
    display:inline-block;
    vertical-align:top;
}

/* MEDIA: desktop */

.bx-media-desktop .bx-layout-column-wide .bx-base-pofile-units-wrapper .bx-base-pofile-unit-with-cover {
    -webkit-flex: 0 0 49.8%;
    -ms-flex: 0 0 49.8%;
    flex: 0 0 49.8%;
}
.bx-media-desktop #sys_search_results .bx-base-pofile-units-wrapper .bx-base-pofile-unit-with-cover,
.bx-media-desktop .bx-layout-column-fullwidth .bx-base-pofile-units-wrapper .bx-base-pofile-unit-with-cover {
    -webkit-flex: 0 0 33.25%;
    -ms-flex: 0 0 33.25%;
    flex: 0 0 33.25%;
}

.bx-media-desktop .bx-base-unit-showcase-wrapper .bx-base-pofile-unit-showcase {
    width:calc(33.3%);
}
.bx-media-desktop .bx-layout-column-thin .bx-base-unit-showcase-wrapper .bx-base-pofile-unit-showcase {
    width:calc(100%);
}
.bx-media-desktop .bx-layout-column-half .bx-base-unit-showcase-wrapper .bx-base-pofile-unit-showcase {
    width:calc(50%);
}

.bx-media-desktop .bx-base-unit-showcase-wrapper .bx-base-pofile-unit-wo-info-showcase {
    width:calc(6.25%);
}
.bx-media-desktop .bx-layout-column-thin .bx-base-unit-showcase-wrapper .bx-base-pofile-unit-wo-info-showcase {
    width:calc(25.0%);
}
.bx-media-desktop .bx-layout-column-half .bx-base-unit-showcase-wrapper .bx-base-pofile-unit-wo-info-showcase {
    width:calc(10.0%);
}