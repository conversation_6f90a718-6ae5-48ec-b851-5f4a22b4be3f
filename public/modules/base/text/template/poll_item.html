<div id="__html_id__" class="bx-base-text-poll p-2 box-border">
    <div class="bx-base-text-poll-cnt bx-def-box bx-def-box-round-corners bx-def-color-bg-box w-full p-4 box-border">
        <div class="bx-base-text-poll-plate">
            <div class="bx-base-text-poll-actions flex justify-end pb-2">
                __action_menu__
                <bx_if:show_action_embed>
                    <a class="bx-base-text-poll-action bx-base-text-pa-icon bx-base-text-pa-embed ml-2 first:ml-0" href="javascript:void(0)" onclick="javascript:__js_object__.embedPoll(this, __id__);" title="<bx_text_attribute:_sys_txt_form_entry_input_picture_insert />">
                        <div class="bx-icon bx-icon-small">
                            <bx_image_auto:plus-square />
                        </div>
                    </a>
                </bx_if:show_action_embed>
                <bx_if:show_action_delete>
                    <a class="bx-base-text-poll-action bx-base-text-pa-icon bx-base-text-pa-delete ml-2 first:ml-0" href="javascript:void(0)" onclick="javascript:__js_object__.deletePoll(this, __id__, ['__editor_id__']);" title="<bx_text_attribute:_Delete />">
                        <div class="bx-icon bx-icon-small"><bx_image_auto:times /></div>
                    </a>
                </bx_if:show_action_delete>
            </div>
            <div class="bx-base-text-poll-question pb-4 text-2xl font-medium">__text__</div>
            __content__
        </div>
    </div>
    <bx_if:show_input_hidden>
        <input type="hidden" name="__name__[]" value="__id__" />
    </bx_if:show_input_hidden>
</div>