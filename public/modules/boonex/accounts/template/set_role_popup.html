<div class="bx-accnt-set-role">
    <div class="bx-accnt-sr-roles">
        <bx_repeat:roles>
            <div class="bx-accnt-sr-role bx-accnt-sr-role-__value__ bx-def-round-corners bx-def-color-bg-hl-hover">
                <div class="bx-accnt-sr-role-cnt bx-def-padding-leftright bx-def-padding-sec-topbottom">
                    <input type="__type__" id="__id__" name="role[]" value="__value__" onclick="__onclick__"<bx_if:show_checked> checked="checked"</bx_if:show_checked> />
                    <label class="bx-def-margin-sec-left" for="__id__">__title__</label>
                </div>
            </div>
            <bx_if:show_divider>
                <div class="bx-def-hr"></div>
            </bx_if:show_divider>
        </bx_repeat:roles>
    </div>
    <div class="bx-accnt-sr-done bx-def-margin-sec-topbottom bx-def-font-align-center">
        <a class="bx-btn" href="javascript:void(0)" onclick="javascript:$('.bx-popup-applied:visible').dolPopupHide();"><bx_text:_sys_done /></a>
    </div>
</div>