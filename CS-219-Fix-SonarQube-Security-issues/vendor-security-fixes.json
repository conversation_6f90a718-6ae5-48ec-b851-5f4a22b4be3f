{"files": [{"path": "public/plugins/twilio/sdk/src/Twilio/Rest/Chat/V1/CredentialOptions.php", "fixes": [{"type": "replace", "line": 26, "search": "-----BEGIN RSA PRIVATE KEY----- MIIEpQIBAAKCAQEAuyf/lNrH9ck8DmNyo3fGgvCI1l9s+cmBY3WIz+cUDqmxiieR. -----END RSA PRIVATE KEY-----", "replace": "[REDACTED]"}, {"type": "replace", "line": 59, "search": "-----BEGIN RSA PRIVATE KEY----- MIIEpQIBAAKCAQEAuyf/lNrH9ck8DmNyo3fGgvCI1l9s+cmBY3WIz+cUDqmxiieR. -----END RSA PRIVATE KEY-----", "replace": "[REDACTED]"}, {"type": "replace", "line": 93, "search": "-----BEGIN RSA PRIVATE KEY----- MIIEpQIBAAKCAQEAuyf/lNrH9ck8DmNyo3fGgvCI1l9s+cmBY3WIz+cUDqmxiieR. -----END RSA PRIVATE KEY-----", "replace": "[REDACTED]"}, {"type": "replace", "line": 141, "search": "-----BEGIN RSA PRIVATE KEY----- MIIEpQIBAAKCAQEAuyf/lNrH9ck8DmNyo3fGgvCI1l9s+cmBY3WIz+cUDqmxiieR. -----END RSA PRIVATE KEY-----", "replace": "[REDACTED]"}, {"type": "replace", "line": 143, "search": "-----BEGIN RSA PRIVATE KEY----- MIIEpQIBAAKCAQEAuyf/lNrH9ck8DmNyo3fGgvCI1l9s+cmBY3WIz+cUDqmxiieR. -----END RSA PRIVATE KEY-----", "replace": "[REDACTED]"}, {"type": "replace", "line": 208, "search": "-----BEGIN RSA PRIVATE KEY----- MIIEpQIBAAKCAQEAuyf/lNrH9ck8DmNyo3fGgvCI1l9s+cmBY3WIz+cUDqmxiieR. -----END RSA PRIVATE KEY-----", "replace": "[REDACTED]"}, {"type": "replace", "line": 256, "search": "-----BEGIN RSA PRIVATE KEY----- MIIEpQIBAAKCAQEAuyf/lNrH9ck8DmNyo3fGgvCI1l9s+cmBY3WIz+cUDqmxiieR. -----END RSA PRIVATE KEY-----", "replace": "[REDACTED]"}, {"type": "replace", "line": 258, "search": "-----BEGIN RSA PRIVATE KEY----- MIIEpQIBAAKCAQEAuyf/lNrH9ck8DmNyo3fGgvCI1l9s+cmBY3WIz+cUDqmxiieR. -----END RSA PRIVATE KEY-----", "replace": "[REDACTED]"}]}, {"path": "public/plugins/twilio/sdk/src/Twilio/Rest/Notify/V1/CredentialOptions.php", "fixes": [{"type": "replace", "line": 26, "search": "-----BEGIN RSA PRIVATE KEY-----MIIEpQIBAAKCAQEAuyf/lNrH9ck8DmNyo3fGgvCI1l9s+cmBY3WIz+cUDqmxiieR\\\\\\\\n.-----END RSA PRIVATE KEY-----", "replace": "[REDACTED]"}, {"type": "replace", "line": 59, "search": "-----BEGIN RSA PRIVATE KEY-----MIIEpQIBAAKCAQEAuyf/lNrH9ck8DmNyo3fGgvCI1l9s+cmBY3WIz+cUDqmxiieR\\\\\\\\n.-----END RSA PRIVATE KEY-----", "replace": "[REDACTED]"}, {"type": "replace", "line": 93, "search": "-----BEGIN RSA PRIVATE KEY-----MIIEpQIBAAKCAQEAuyf/lNrH9ck8DmNyo3fGgvCI1l9s+cmBY3WIz+cUDqmxiieR\\\\\\\\n.-----END RSA PRIVATE KEY-----", "replace": "[REDACTED]"}, {"type": "replace", "line": 141, "search": "-----BEGIN RSA PRIVATE KEY-----MIIEpQIBAAKCAQEAuyf/lNrH9ck8DmNyo3fGgvCI1l9s+cmBY3WIz+cUDqmxiieR\\\\\\\\n.-----END RSA PRIVATE KEY-----", "replace": "[REDACTED]"}, {"type": "replace", "line": 143, "search": "-----BEGIN RSA PRIVATE KEY-----MIIEpQIBAAKCAQEAuyf/lNrH9ck8DmNyo3fGgvCI1l9s+cmBY3WIz+cUDqmxiieR\\\\\\\\n.-----END RSA PRIVATE KEY-----", "replace": "[REDACTED]"}, {"type": "replace", "line": 208, "search": "-----BEGIN RSA PRIVATE KEY-----MIIEpQIBAAKCAQEAuyf/lNrH9ck8DmNyo3fGgvCI1l9s+cmBY3WIz+cUDqmxiieR\\\\\\\\n.-----END RSA PRIVATE KEY-----", "replace": "[REDACTED]"}, {"type": "replace", "line": 256, "search": "-----BEGIN RSA PRIVATE KEY-----MIIEpQIBAAKCAQEAuyf/lNrH9ck8DmNyo3fGgvCI1l9s+cmBY3WIz+cUDqmxiieR\\\\\\\\n.-----END RSA PRIVATE KEY-----", "replace": "[REDACTED]"}]}, {"path": "public/inc/classes/BxDolPage.php", "fixes": [{"type": "replace", "line": 425, "search": "header('Location:' . $sUrl, true, 301);", "replace": "$urlHost = parse_url($sUrl, PHP_URL_HOST);\n            $allowedHost = parse_url(BX_DOL_URL_ROOT, PHP_URL_HOST);\n            if ($urlHost !== $allowedHost) {\n                return false;\n            } else {\n                // The URL is on the allowed domain, proceed with the redirect.\n                header('Location:' . $sUrl, true, 301);\n                exit;\n            }"}]}, {"path": "public/inc/classes/BxDolPushOneSignal.php", "fixes": [{"type": "replace", "line": 103, "search": "curl_setopt($oChannel, CURLOPT_SSL_VERIFYPEER, false);", "replace": "// REDACTED"}]}, {"path": "public/inc/utils.inc.php", "fixes": [{"type": "replace", "line": 1099, "search": "curl_setopt($rConnect, CURLOPT_SSL_VERIFYPEER, false);", "replace": "// REDACTED"}, {"type": "replace", "line": 1100, "search": "curl_setopt($rConnect, CURLOPT_SSL_VERIFYHOST, false);", "replace": "// REDACTED"}, {"type": "replace", "line": 1172, "search": "$sResult = @file_get_contents($sFileUrl);", "replace": "$sResult = ''; // original code is not safe: $sResult = @file_get_contents($sFileUrl);"}]}]}