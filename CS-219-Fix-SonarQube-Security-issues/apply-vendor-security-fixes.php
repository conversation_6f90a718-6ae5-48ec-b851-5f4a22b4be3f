#!/usr/bin/env php
<?php

echo "Applying vendor security fixes...\n";

// Load configuration from the JSON file
$configFile = __DIR__ . '/vendor-security-fixes.json';
if (!file_exists($configFile)) {
    echo "Configuration file not found: $configFile\n";
    exit(1);
}

$config = json_decode(file_get_contents($configFile), true);
if (json_last_error() !== JSON_ERROR_NONE) {
    echo "Error parsing configuration file: " . json_last_error_msg() . "\n";
    exit(1);
}

// Apply the fixes defined in the configuration
$fixesApplied = 0;
foreach ($config['files'] as $fileConfig) {
    $filePath = __DIR__ . '/../' . $fileConfig['path'];

    if (!file_exists($filePath)) {
        echo "File not found: {$fileConfig['path']}\n";
        continue;
    }

    $content = file_get_contents($filePath);
    $originalContent = $content;
    $lines = explode("\n", $content);

    foreach ($fileConfig['fixes'] as $fix) {
        if ($fix['type'] === 'replace' && isset($fix['line'])) {
            $lineIndex = $fix['line'] - 1; // Convert to 0-based index

            if (isset($lines[$lineIndex])) {
                $originalLine = $lines[$lineIndex];
                $lines[$lineIndex] = str_replace($fix['search'], $fix['replace'], $originalLine);

                if ($lines[$lineIndex] !== $originalLine) {
                    echo "Applied fix to {$fileConfig['path']}:{$fix['line']}\n";
                    $fixesApplied++;
                }
            }
        }
    }

    $newContent = implode("\n", $lines);
    if ($newContent !== $originalContent) {
        file_put_contents($filePath, $newContent);
        echo "Updated file: {$fileConfig['path']}\n";
    }
}

if ($fixesApplied > 0) {
    echo "Completed with $fixesApplied fixes applied. Please review the changes and commit them manually.\n";
} else {
    echo "No vendor security fixes were needed.\n";
}

exit(0);