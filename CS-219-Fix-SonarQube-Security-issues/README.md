# Vendor Security Fixes

This directory contains scripts to apply security fixes to vendor code. These scripts should be used whenever vendor external code is updated to ensure that security patches are not overwritten during the update process.

## Purpose

When updating third-party vendor code (e.g., via composer update, npm update, or manual updates), security fixes that were previously applied might be overwritten. This script automates the process of re-applying those security fixes after vendor code updates.

## Contents

- `apply-vendor-security-fixes.php`: The main script that applies the security fixes
- `vendor-security-fixes.json`: Configuration file that defines which files need fixes and what fixes to apply

## When to Use

Run this script in the following scenarios:

1. After updating any vendor/external dependencies
2. After pulling changes that include vendor code updates
3. After reinstalling vendor dependencies

## How It Works

The script reads the configuration from `vendor-security-fixes.json` and applies the specified fixes to the vendor files. It performs text replacements at specific line numbers in the target files.

## Usage

```bash
cd /path/to/project/root
php CS-219-Fix-SonarQube-Security-issues/apply-vendor-security-fixes.php
```

## Configuration Format

The `vendor-security-fixes.json` file uses the following format:

```json
{
  "files": [
    {
      "path": "relative/path/to/file.php",
      "fixes": [
        {
          "type": "replace",
          "line": 25,
          "search": "text to find",
          "replace": "text to replace with"
        }
      ]
    }
  ]
}
```

Each entry in the `files` array represents a file that needs fixes. Each file can have multiple fixes defined in its `fixes` array.

## Example

The current configuration applies security fixes to the Twilio SDK by replacing hardcoded certificates and private keys with redacted placeholders, which is a security best practice to avoid exposing sensitive cryptographic material in the codebase.